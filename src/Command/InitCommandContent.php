<?php

namespace Flexiwind\Command;

/**
 * This file contains the code content that will be integrated into InitCommand
 * Organized by sections for easy integration
 */
class InitCommandContent
{
    /**
     * Laravel Helper Class Content
     */
    public static function getLaravelHelpersContent(): string
    {
        return <<<'PHP'
<?php

namespace App\Flexiwind;

class Helpers 
{
    /**
     * Generate Flexiwind component classes
     */
    public static function component(string $name, array $variants = []): string
    {
        $baseClasses = self::getComponentClasses($name);
        $variantClasses = self::getVariantClasses($name, $variants);
        
        return trim($baseClasses . ' ' . $variantClasses);
    }
    
    /**
     * Get base component classes
     */
    private static function getComponentClasses(string $name): string
    {
        $components = [
            'button' => 'px-4 py-2 rounded font-medium transition-colors',
            'card' => 'bg-white rounded-lg shadow-md p-6',
            'input' => 'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2',
            'badge' => 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
        ];
        
        return $components[$name] ?? '';
    }
    
    /**
     * Get variant classes
     */
    private static function getVariantClasses(string $name, array $variants): string
    {
        $variantMap = [
            'button' => [
                'primary' => 'bg-blue-600 text-white hover:bg-blue-700',
                'secondary' => 'bg-gray-200 text-gray-900 hover:bg-gray-300',
                'danger' => 'bg-red-600 text-white hover:bg-red-700',
                'small' => 'px-2 py-1 text-sm',
                'large' => 'px-6 py-3 text-lg',
            ],
            'card' => [
                'elevated' => 'shadow-lg',
                'bordered' => 'border border-gray-200',
            ],
            'input' => [
                'error' => 'border-red-500 focus:ring-red-500',
                'success' => 'border-green-500 focus:ring-green-500',
            ],
            'badge' => [
                'primary' => 'bg-blue-100 text-blue-800',
                'success' => 'bg-green-100 text-green-800',
                'warning' => 'bg-yellow-100 text-yellow-800',
                'danger' => 'bg-red-100 text-red-800',
            ],
        ];
        
        $classes = [];
        foreach ($variants as $variant) {
            if (isset($variantMap[$name][$variant])) {
                $classes[] = $variantMap[$name][$variant];
            }
        }
        
        return implode(' ', $classes);
    }
    
    /**
     * Legacy hello method for backward compatibility
     */
    public static function hello(): string
    {
        return "Hello from Flexiwind!";
    }
}
PHP;
    }

    /**
     * Custom PHP Helpers Content
     */
    public static function getCustomHelpersContent(): string
    {
        return <<<'PHP'
<?php

/**
 * Flexiwind Helper Functions
 */

if (!function_exists('flexiwind_component')) {
    function flexiwind_component(string $name, array $variants = []): string
    {
        $baseClasses = flexiwind_get_component_classes($name);
        $variantClasses = flexiwind_get_variant_classes($name, $variants);
        
        return trim($baseClasses . ' ' . $variantClasses);
    }
}

if (!function_exists('flexiwind_get_component_classes')) {
    function flexiwind_get_component_classes(string $name): string
    {
        $components = [
            'button' => 'px-4 py-2 rounded font-medium transition-colors',
            'card' => 'bg-white rounded-lg shadow-md p-6',
            'input' => 'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2',
            'badge' => 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
        ];
        
        return $components[$name] ?? '';
    }
}

if (!function_exists('flexiwind_get_variant_classes')) {
    function flexiwind_get_variant_classes(string $name, array $variants): string
    {
        $variantMap = [
            'button' => [
                'primary' => 'bg-blue-600 text-white hover:bg-blue-700',
                'secondary' => 'bg-gray-200 text-gray-900 hover:bg-gray-300',
                'danger' => 'bg-red-600 text-white hover:bg-red-700',
                'small' => 'px-2 py-1 text-sm',
                'large' => 'px-6 py-3 text-lg',
            ],
            'card' => [
                'elevated' => 'shadow-lg',
                'bordered' => 'border border-gray-200',
            ],
            'input' => [
                'error' => 'border-red-500 focus:ring-red-500',
                'success' => 'border-green-500 focus:ring-green-500',
            ],
            'badge' => [
                'primary' => 'bg-blue-100 text-blue-800',
                'success' => 'bg-green-100 text-green-800',
                'warning' => 'bg-yellow-100 text-yellow-800',
                'danger' => 'bg-red-100 text-red-800',
            ],
        ];
        
        $classes = [];
        foreach ($variants as $variant) {
            if (isset($variantMap[$name][$variant])) {
                $classes[] = $variantMap[$name][$variant];
            }
        }
        
        return implode(' ', $classes);
    }
}

if (!function_exists('flexiwind_hello')) {
    function flexiwind_hello(): string
    {
        return 'Hello from Flexiwind!';
    }
}
PHP;
    }

    /**
     * JavaScript Content for Flexilla
     */
    public static function getFlexillaJsContent(): string
    {
        return <<<'JS'
/**
 * Flexiwind JavaScript Bootstrap
 * Provides component initialization and utilities
 */

class Flexiwind {
    constructor() {
        this.components = new Map();
        this.init();
    }

    init() {
        // Initialize components when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeComponents());
        } else {
            this.initializeComponents();
        }
    }

    initializeComponents() {
        // Initialize dropdowns
        this.initDropdowns();
        
        // Initialize modals
        this.initModals();
        
        // Initialize tooltips
        this.initTooltips();
        
        console.log('Flexiwind components initialized');
    }

    initDropdowns() {
        const dropdowns = document.querySelectorAll('[data-flexiwind="dropdown"]');
        dropdowns.forEach(dropdown => {
            const trigger = dropdown.querySelector('[data-dropdown-trigger]');
            const menu = dropdown.querySelector('[data-dropdown-menu]');
            
            if (trigger && menu) {
                trigger.addEventListener('click', (e) => {
                    e.stopPropagation();
                    menu.classList.toggle('hidden');
                });
                
                // Close on outside click
                document.addEventListener('click', () => {
                    menu.classList.add('hidden');
                });
            }
        });
    }

    initModals() {
        const modals = document.querySelectorAll('[data-flexiwind="modal"]');
        modals.forEach(modal => {
            const triggers = document.querySelectorAll(`[data-modal-target="${modal.id}"]`);
            const closeButtons = modal.querySelectorAll('[data-modal-close]');
            
            triggers.forEach(trigger => {
                trigger.addEventListener('click', () => {
                    modal.classList.remove('hidden');
                });
            });
            
            closeButtons.forEach(button => {
                button.addEventListener('click', () => {
                    modal.classList.add('hidden');
                });
            });
            
            // Close on backdrop click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        });
    }

    initTooltips() {
        const tooltips = document.querySelectorAll('[data-flexiwind="tooltip"]');
        tooltips.forEach(element => {
            const text = element.getAttribute('data-tooltip-text');
            if (text) {
                element.addEventListener('mouseenter', () => {
                    this.showTooltip(element, text);
                });
                
                element.addEventListener('mouseleave', () => {
                    this.hideTooltip();
                });
            }
        });
    }

    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'flexiwind-tooltip absolute z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg';
        tooltip.textContent = text;
        tooltip.id = 'flexiwind-tooltip';
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
    }

    hideTooltip() {
        const tooltip = document.getElementById('flexiwind-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }
}

// Initialize Flexiwind
const flexiwind = new Flexiwind();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Flexiwind;
}
JS;
    }

    /**
     * Base Flexiwind CSS Content
     */
    public static function getFlexiwindCssContent(): string
    {
        return <<<'CSS'
/**
 * Flexiwind Base Styles
 * Core styling foundation for Flexiwind components
 */

/* Reset and base styles */
.flexiwind-reset {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* Layout utilities */
.flexiwind-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.flexiwind-grid {
    display: grid;
    gap: 1rem;
}

.flexiwind-flex {
    display: flex;
    gap: 0.5rem;
}

/* Typography */
.flexiwind-text-xs { font-size: 0.75rem; }
.flexiwind-text-sm { font-size: 0.875rem; }
.flexiwind-text-base { font-size: 1rem; }
.flexiwind-text-lg { font-size: 1.125rem; }
.flexiwind-text-xl { font-size: 1.25rem; }
.flexiwind-text-2xl { font-size: 1.5rem; }

/* Spacing */
.flexiwind-p-1 { padding: 0.25rem; }
.flexiwind-p-2 { padding: 0.5rem; }
.flexiwind-p-3 { padding: 0.75rem; }
.flexiwind-p-4 { padding: 1rem; }
.flexiwind-p-6 { padding: 1.5rem; }

.flexiwind-m-1 { margin: 0.25rem; }
.flexiwind-m-2 { margin: 0.5rem; }
.flexiwind-m-3 { margin: 0.75rem; }
.flexiwind-m-4 { margin: 1rem; }
.flexiwind-m-6 { margin: 1.5rem; }

/* Colors */
.flexiwind-bg-primary { background-color: #3b82f6; }
.flexiwind-bg-secondary { background-color: #6b7280; }
.flexiwind-bg-success { background-color: #10b981; }
.flexiwind-bg-warning { background-color: #f59e0b; }
.flexiwind-bg-danger { background-color: #ef4444; }

.flexiwind-text-primary { color: #3b82f6; }
.flexiwind-text-secondary { color: #6b7280; }
.flexiwind-text-success { color: #10b981; }
.flexiwind-text-warning { color: #f59e0b; }
.flexiwind-text-danger { color: #ef4444; }

/* Borders */
.flexiwind-border { border: 1px solid #e5e7eb; }
.flexiwind-border-primary { border-color: #3b82f6; }
.flexiwind-border-danger { border-color: #ef4444; }

.flexiwind-rounded { border-radius: 0.25rem; }
.flexiwind-rounded-md { border-radius: 0.375rem; }
.flexiwind-rounded-lg { border-radius: 0.5rem; }
.flexiwind-rounded-full { border-radius: 9999px; }

/* Shadows */
.flexiwind-shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); }
.flexiwind-shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
.flexiwind-shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }

/* Transitions */
.flexiwind-transition {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

/* Focus styles */
.flexiwind-focus:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #3b82f6;
}

/* Responsive utilities */
@media (min-width: 640px) {
    .flexiwind-sm\:block { display: block; }
    .flexiwind-sm\:hidden { display: none; }
}

@media (min-width: 768px) {
    .flexiwind-md\:block { display: block; }
    .flexiwind-md\:hidden { display: none; }
}

@media (min-width: 1024px) {
    .flexiwind-lg\:block { display: block; }
    .flexiwind-lg\:hidden { display: none; }
}
CSS;
    }

    /**
     * Button Component CSS Content
     */
    public static function getButtonCssContent(): string
    {
        return <<<'CSS'
/**
 * Flexiwind Button Styles
 * Comprehensive button component styling
 */

/* Base button styles */
.flexiwind-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.25rem;
    border-radius: 0.375rem;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    text-decoration: none;
    user-select: none;
}

.flexiwind-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Button variants */
.flexiwind-btn-primary {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.flexiwind-btn-primary:hover:not(:disabled) {
    background-color: #2563eb;
    border-color: #2563eb;
}

.flexiwind-btn-secondary {
    background-color: #6b7280;
    color: white;
    border-color: #6b7280;
}

.flexiwind-btn-secondary:hover:not(:disabled) {
    background-color: #4b5563;
    border-color: #4b5563;
}

.flexiwind-btn-outline {
    background-color: transparent;
    color: #3b82f6;
    border-color: #3b82f6;
}

.flexiwind-btn-outline:hover:not(:disabled) {
    background-color: #3b82f6;
    color: white;
}

.flexiwind-btn-ghost {
    background-color: transparent;
    color: #374151;
    border-color: transparent;
}

.flexiwind-btn-ghost:hover:not(:disabled) {
    background-color: #f3f4f6;
}

.flexiwind-btn-danger {
    background-color: #ef4444;
    color: white;
    border-color: #ef4444;
}

.flexiwind-btn-danger:hover:not(:disabled) {
    background-color: #dc2626;
    border-color: #dc2626;
}

/* Button sizes */
.flexiwind-btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.flexiwind-btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.flexiwind-btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

.flexiwind-btn-xl {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

/* Button with icons */
.flexiwind-btn-icon {
    padding: 0.5rem;
    width: 2.5rem;
    height: 2.5rem;
}

.flexiwind-btn .flexiwind-icon {
    width: 1rem;
    height: 1rem;
}

.flexiwind-btn .flexiwind-icon:first-child {
    margin-right: 0.5rem;
}

.flexiwind-btn .flexiwind-icon:last-child {
    margin-left: 0.5rem;
}

/* Loading state */
.flexiwind-btn-loading {
    position: relative;
    color: transparent;
}

.flexiwind-btn-loading::after {
    content: '';
    position: absolute;
    width: 1rem;
    height: 1rem;
    top: 50%;
    left: 50%;
    margin-left: -0.5rem;
    margin-top: -0.5rem;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: flexiwind-spin 1s linear infinite;
}

@keyframes flexiwind-spin {
    to {
        transform: rotate(360deg);
    }
}

/* Button groups */
.flexiwind-btn-group {
    display: inline-flex;
}

.flexiwind-btn-group .flexiwind-btn {
    border-radius: 0;
    border-right-width: 0;
}

.flexiwind-btn-group .flexiwind-btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.flexiwind-btn-group .flexiwind-btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
    border-right-width: 1px;
}
CSS;
    }

    /**
     * Utility Classes CSS Content
     */
    public static function getUtilitiesCssContent(): string
    {
        return <<<'CSS'
/**
 * Flexiwind Utility Classes
 * Additional utility classes for common styling needs
 */

/* Display utilities */
.flexiwind-block { display: block; }
.flexiwind-inline { display: inline; }
.flexiwind-inline-block { display: inline-block; }
.flexiwind-flex { display: flex; }
.flexiwind-inline-flex { display: inline-flex; }
.flexiwind-grid { display: grid; }
.flexiwind-hidden { display: none; }

/* Flex utilities */
.flexiwind-flex-row { flex-direction: row; }
.flexiwind-flex-col { flex-direction: column; }
.flexiwind-flex-wrap { flex-wrap: wrap; }
.flexiwind-flex-nowrap { flex-wrap: nowrap; }

.flexiwind-items-start { align-items: flex-start; }
.flexiwind-items-center { align-items: center; }
.flexiwind-items-end { align-items: flex-end; }
.flexiwind-items-stretch { align-items: stretch; }

.flexiwind-justify-start { justify-content: flex-start; }
.flexiwind-justify-center { justify-content: center; }
.flexiwind-justify-end { justify-content: flex-end; }
.flexiwind-justify-between { justify-content: space-between; }
.flexiwind-justify-around { justify-content: space-around; }

.flexiwind-flex-1 { flex: 1 1 0%; }
.flexiwind-flex-auto { flex: 1 1 auto; }
.flexiwind-flex-none { flex: none; }

/* Position utilities */
.flexiwind-static { position: static; }
.flexiwind-relative { position: relative; }
.flexiwind-absolute { position: absolute; }
.flexiwind-fixed { position: fixed; }
.flexiwind-sticky { position: sticky; }

/* Width and height utilities */
.flexiwind-w-auto { width: auto; }
.flexiwind-w-full { width: 100%; }
.flexiwind-w-1\/2 { width: 50%; }
.flexiwind-w-1\/3 { width: 33.333333%; }
.flexiwind-w-2\/3 { width: 66.666667%; }
.flexiwind-w-1\/4 { width: 25%; }
.flexiwind-w-3\/4 { width: 75%; }

.flexiwind-h-auto { height: auto; }
.flexiwind-h-full { height: 100%; }
.flexiwind-h-screen { height: 100vh; }

/* Text alignment */
.flexiwind-text-left { text-align: left; }
.flexiwind-text-center { text-align: center; }
.flexiwind-text-right { text-align: right; }
.flexiwind-text-justify { text-align: justify; }

/* Font weight */
.flexiwind-font-thin { font-weight: 100; }
.flexiwind-font-light { font-weight: 300; }
.flexiwind-font-normal { font-weight: 400; }
.flexiwind-font-medium { font-weight: 500; }
.flexiwind-font-semibold { font-weight: 600; }
.flexiwind-font-bold { font-weight: 700; }
.flexiwind-font-extrabold { font-weight: 800; }
.flexiwind-font-black { font-weight: 900; }

/* Overflow utilities */
.flexiwind-overflow-auto { overflow: auto; }
.flexiwind-overflow-hidden { overflow: hidden; }
.flexiwind-overflow-visible { overflow: visible; }
.flexiwind-overflow-scroll { overflow: scroll; }

.flexiwind-overflow-x-auto { overflow-x: auto; }
.flexiwind-overflow-x-hidden { overflow-x: hidden; }
.flexiwind-overflow-y-auto { overflow-y: auto; }
.flexiwind-overflow-y-hidden { overflow-y: hidden; }

/* Z-index utilities */
.flexiwind-z-0 { z-index: 0; }
.flexiwind-z-10 { z-index: 10; }
.flexiwind-z-20 { z-index: 20; }
.flexiwind-z-30 { z-index: 30; }
.flexiwind-z-40 { z-index: 40; }
.flexiwind-z-50 { z-index: 50; }

/* Opacity utilities */
.flexiwind-opacity-0 { opacity: 0; }
.flexiwind-opacity-25 { opacity: 0.25; }
.flexiwind-opacity-50 { opacity: 0.5; }
.flexiwind-opacity-75 { opacity: 0.75; }
.flexiwind-opacity-100 { opacity: 1; }

/* Cursor utilities */
.flexiwind-cursor-auto { cursor: auto; }
.flexiwind-cursor-default { cursor: default; }
.flexiwind-cursor-pointer { cursor: pointer; }
.flexiwind-cursor-wait { cursor: wait; }
.flexiwind-cursor-text { cursor: text; }
.flexiwind-cursor-move { cursor: move; }
.flexiwind-cursor-not-allowed { cursor: not-allowed; }

/* Select utilities */
.flexiwind-select-none { user-select: none; }
.flexiwind-select-text { user-select: text; }
.flexiwind-select-all { user-select: all; }
.flexiwind-select-auto { user-select: auto; }

/* Pointer events */
.flexiwind-pointer-events-none { pointer-events: none; }
.flexiwind-pointer-events-auto { pointer-events: auto; }

/* Visibility */
.flexiwind-visible { visibility: visible; }
.flexiwind-invisible { visibility: hidden; }

/* Animation utilities */
.flexiwind-animate-spin {
    animation: flexiwind-spin 1s linear infinite;
}

.flexiwind-animate-ping {
    animation: flexiwind-ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.flexiwind-animate-pulse {
    animation: flexiwind-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.flexiwind-animate-bounce {
    animation: flexiwind-bounce 1s infinite;
}

@keyframes flexiwind-ping {
    75%, 100% {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes flexiwind-pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

@keyframes flexiwind-bounce {
    0%, 100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: translateY(0);
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

/* Screen reader utilities */
.flexiwind-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.flexiwind-not-sr-only {
    position: static;
    width: auto;
    height: auto;
    padding: 0;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
}
CSS;
    }
}
